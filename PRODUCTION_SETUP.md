# Production Setup Guide

This guide helps you deploy and configure ACHY.ME in production to fix the share bio and thanks page loading issues.

## 🚨 Issues Fixed

- **Share Bio Not Showing**: Fixed organization context resolution
- **Thanks Page Loading Forever**: Fixed API endpoints and organization context
- **Production Domain Configuration**: Added proper domain handling

## 🚀 Deployment Steps

### 1. Environment Variables

Copy the environment variables from `.env.vercel.example` to your Vercel project:

```bash
# Required Variables
NEXTAUTH_URL=https://your-domain.vercel.app
NEXTAUTH_SECRET=your-32-character-secret-key
PRODUCTION_DOMAIN=your-domain.vercel.app
MONGODB_URI=mongodb+srv://username:<EMAIL>/database
CLOUDFLARE_ACCOUNT_ID=your-account-id
CLOUDFLARE_ACCESS_KEY_ID=your-access-key
CLOUDFLARE_SECRET_ACCESS_KEY=your-secret-key
CLOUDFLARE_BUCKET_NAME=your-bucket-name
CLOUDFLARE_R2_ENDPOINT=https://account-id.r2.cloudflarestorage.com
OPENAI_API_KEY=sk-your-openai-key
```

### 2. Deploy to Vercel

```bash
# Deploy the application
vercel --prod

# Or if using GitHub integration, push to main branch
git push origin main
```

### 3. Setup Organization (CRITICAL)

After deployment, run the organization setup script:

```bash
# Set environment variables locally
export MONGODB_URI="your-mongodb-connection-string"
export PRODUCTION_DOMAIN="your-actual-domain.vercel.app"

# Run the setup script
node scripts/setup-production-org.js
```

This script will:
- Connect to your production database
- Create or update the organization for your domain
- Ensure proper configuration for multi-tenant isolation

### 4. Verify Setup

1. **Check Organization**: Visit your deployed app and check if it loads properly
2. **Test Share Bio**: Go to Profile page and click "Share Bio" button
3. **Test Thanks Page**: Visit `/thanks` and ensure it loads without infinite loading
4. **Test Bio Sharing**: Try accessing a bio URL like `/bio/[user-id]`

## 🔧 Troubleshooting

### Share Bio Still Not Working

1. Check browser console for errors
2. Verify organization exists in database:
   ```javascript
   // In MongoDB shell or Compass
   db.organizations.find({ domain: "your-domain.vercel.app" })
   ```

### Thanks Page Still Loading

1. Check API responses in Network tab
2. Verify `/api/thanks` returns data (not empty array)
3. Check server logs for organization context errors

### Organization Context Issues

The app now includes fallback logic:
- If exact domain match fails, it uses the first active organization
- Logs are added to help debug organization resolution issues

### Manual Organization Creation

If the script fails, manually create organization in MongoDB:

```javascript
db.organizations.insertOne({
  name: "ACHY.ME",
  domain: "your-domain.vercel.app",
  subdomain: "app",
  description: "Employee appreciation platform",
  industry: "Technology",
  size: "small",
  settings: {
    allowPublicProfiles: true,
    requireEmailVerification: false,
    enableAchievements: true,
    enableThanks: true,
    customBranding: false,
    maxEmployees: 1000
  },
  subscription: {
    plan: "premium",
    status: "active",
    startDate: new Date(),
    features: ["thanks", "achievements", "analytics", "public_profiles"]
  },
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
})
```

## 📝 Environment Variables Reference

| Variable | Required | Description |
|----------|----------|-------------|
| `NEXTAUTH_URL` | ✅ | Your production URL |
| `NEXTAUTH_SECRET` | ✅ | Random 32+ character string |
| `PRODUCTION_DOMAIN` | ✅ | Your domain (without https://) |
| `MONGODB_URI` | ✅ | MongoDB connection string |
| `CLOUDFLARE_*` | ✅ | R2 storage configuration |
| `OPENAI_API_KEY` | ⚠️ | Optional, for AI features |

## 🎯 Key Changes Made

1. **Organization Middleware**: Added fallback logic for production
2. **API Error Handling**: Better logging for debugging
3. **Production Script**: Automated organization setup
4. **Environment Config**: Clear production variables

## 📞 Support

If you continue experiencing issues:

1. Check Vercel deployment logs
2. Check MongoDB connection and data
3. Verify all environment variables are set
4. Run the organization setup script again

The fixes ensure that even if domain configuration isn't perfect, the app will find an active organization and work properly.
