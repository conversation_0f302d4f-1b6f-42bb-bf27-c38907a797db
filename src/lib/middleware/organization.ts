import { NextRequest } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Organization, { IOrganization } from '@/models/Organization';

export interface OrganizationContext {
  organization: IOrganization;
  subdomain: string | null;
  domain: string;
}

/**
 * Extract organization from request based on subdomain or domain
 */
export async function getOrganizationFromRequest(request: NextRequest): Promise<OrganizationContext | null> {
  try {
    await dbConnect();

    const host = request.headers.get('host') || '';
    const url = new URL(request.url);

    // Extract subdomain and domain
    let subdomain = '';
    let domain = '';

    // Check if it's a subdomain request (e.g., company.example.com)
    const hostParts = host.split('.');

    if (hostParts.length >= 3) {
      // Subdomain format: company.example.com
      subdomain = hostParts[0];
      domain = hostParts.slice(1).join('.');
    } else if (hostParts.length === 2) {
      // Direct domain format: company.com
      domain = host;
    } else {
      // Localhost or development
      // Check for subdomain in URL path or query params
      const subdomainParam = url.searchParams.get('org') || url.searchParams.get('subdomain');
      if (subdomainParam) {
        subdomain = subdomainParam;
      } else {
        // Default to localhost development
        subdomain = 'demo';
      }
    }

    let organization: IOrganization | null = null;

    // ENHANCED: For localhost, try to determine organization from user's email domain first
    if (host.includes('localhost') || host.includes('127.0.0.1')) {
      // Try to get user's email from session to determine their organization
      const { getServerSession } = await import('next-auth');
      const { authOptions } = await import('@/lib/auth');

      try {
        const session = await getServerSession(authOptions);
        if (session?.user?.email) {
          const userEmailDomain = session.user.email.split('@')[1];

          // Try to find organization by user's email domain
          const orgByEmailDomain = await Organization.findOne({
            domain: userEmailDomain.toLowerCase(),
            isActive: true
          });

          if (orgByEmailDomain) {
            organization = orgByEmailDomain;
          }
        }
      } catch (error) {
        // Silently continue if session detection fails
      }
    }

    // Try to find organization by subdomain first (if not already found)
    if (!organization && subdomain) {
      organization = await Organization.findOne({
        subdomain: subdomain.toLowerCase(),
        isActive: true
      });
    }

    // If not found by subdomain, try by domain
    if (!organization && domain) {
      organization = await Organization.findOne({
        domain: domain.toLowerCase(),
        isActive: true
      });
    }

    // If still not found and in development, create a demo organization
    if (!organization && (host.includes('localhost') || host.includes('127.0.0.1'))) {
      organization = await createDemoOrganization();
    }

    // PRODUCTION FIX: If no organization found, try to find any active organization as fallback
    // This ensures the app works even if domain configuration is not perfect
    if (!organization) {
      console.warn(`No organization found for host: ${host}, domain: ${domain}, subdomain: ${subdomain}`);

      // Try to find any active organization as fallback
      organization = await Organization.findOne({
        isActive: true,
        'subscription.status': 'active'
      }).sort({ createdAt: 1 }); // Get the oldest (likely primary) organization

      if (organization) {
        console.log(`Using fallback organization: ${organization.name} (${organization.domain})`);
      }
    }

    if (!organization) {
      console.error('No organization found and no fallback available');
      return null;
    }

    return {
      organization,
      subdomain: subdomain || null,
      domain: domain || host
    };

  } catch (error) {
    console.error('Error getting organization from request:', error);
    return null;
  }
}

/**
 * Create a demo organization for development
 */
async function createDemoOrganization(): Promise<IOrganization> {
  try {
    // Check if demo organization already exists
    let demoOrg = await Organization.findOne({ subdomain: 'demo' });

    if (!demoOrg) {
      demoOrg = new Organization({
        name: 'Demo Company',
        domain: 'demo.localhost',
        subdomain: 'demo',
        description: 'Demo organization for development and testing',
        industry: 'Technology',
        size: 'small',
        settings: {
          allowPublicProfiles: true,
          requireEmailVerification: false,
          enableAchievements: true,
          enableThanks: true,
          customBranding: false,
          maxEmployees: 100,
        },
        subscription: {
          plan: 'premium',
          status: 'active',
          startDate: new Date(),
          features: ['thanks', 'achievements', 'analytics', 'custom_branding'],
        },
        isActive: true,
      });
      
      await demoOrg.save();
      console.log('Created demo organization for development');
    }
    
    return demoOrg;
  } catch (error) {
    console.error('Error creating demo organization:', error);
    throw error;
  }
}

/**
 * Middleware to require organization context
 */
export function withOrganization(
  handler: (request: NextRequest, context: { organization: IOrganization }) => Promise<Response>
) {
  return async (request: NextRequest): Promise<Response> => {
    const orgContext = await getOrganizationFromRequest(request);
    
    if (!orgContext) {
      return new Response(
        JSON.stringify({
          error: 'Organization not found',
          message: 'Please check your domain configuration'
        }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Check if organization is active
    if (!orgContext.organization.isActive) {
      return new Response(
        JSON.stringify({ 
          error: 'Organization inactive',
          message: 'This organization account is currently inactive'
        }),
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Check subscription status
    if (orgContext.organization.subscription.status !== 'active') {
      return new Response(
        JSON.stringify({ 
          error: 'Subscription inactive',
          message: 'This organization subscription is not active'
        }),
        { 
          status: 402,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return handler(request, { organization: orgContext.organization });
  };
}

/**
 * Get organization ID from request
 */
export async function getOrganizationId(request: NextRequest): Promise<string | null> {
  const orgContext = await getOrganizationFromRequest(request);
  return orgContext?.organization._id || null;
}

/**
 * Validate that user belongs to organization
 */
export async function validateUserOrganization(
  userEmail: string, 
  organizationId: string
): Promise<boolean> {
  try {
    await dbConnect();
    
    const Employee = (await import('@/models/Employee')).default;
    const employee = await Employee.findOne({
      email: userEmail,
      organization: organizationId,
      isActive: true
    });
    
    return !!employee;
  } catch (error) {
    console.error('Error validating user organization:', error);
    return false;
  }
}

export default {
  getOrganizationFromRequest,
  withOrganization,
  getOrganizationId,
  validateUserOrganization,
  createDemoOrganization
};
