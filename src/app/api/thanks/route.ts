import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Thanks from '@/models/Thanks';
import Employee from '@/models/Employee';
import NotificationService from '@/lib/notifications';
import { getOrganizationFromRequest } from '@/lib/middleware/organization';

export async function GET(request: NextRequest) {
  try {
    // Allow both authenticated and unauthenticated users to view public thanks
    const session = await getServerSession(authOptions);

    await dbConnect();

    // Get organization context for both authenticated and anonymous users
    const orgContext = await getOrganizationFromRequest(request);
    if (!orgContext) {
      console.error('Organization context not found for thanks request:', {
        host: request.headers.get('host'),
        url: request.url,
        userAgent: request.headers.get('user-agent')
      });
      // If no organization context found, return empty result for security
      return NextResponse.json({
        thanks: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          pages: 0
        }
      });
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const toEmployee = searchParams.get('toEmployee');
    const fromEmployee = searchParams.get('fromEmployee');
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');

    // Get current user's employee record if authenticated
    let currentEmployee = null;
    if (session?.user?.email) {
      currentEmployee = await Employee.findOne({
        email: session.user.email,
        organization: orgContext.organization._id
      });
    }

    // Build privacy filter
    let privacyFilter: any;
    if (currentEmployee) {
      if (toEmployee) {
        // When viewing a specific user's thanks, show:
        // 1. All public thanks for that user
        // 2. Private thanks for that user where current user is the sender
        // 3. Private thanks for that user where current user is the recipient (i.e., the target user)
        privacyFilter = {
          $or: [
            { isPublic: true },
            {
              isPublic: false,
              $or: [
                { fromEmployee: currentEmployee._id }, // Current user sent private thanks to target
                { toEmployee: currentEmployee._id }     // Current user is viewing their own private thanks
              ]
            }
          ]
        };
      } else {
        // When viewing general thanks board, show:
        // 1. All public thanks
        // 2. Private thanks where current user is sender OR recipient
        privacyFilter = {
          $or: [
            { isPublic: true },
            {
              isPublic: false,
              $or: [
                { fromEmployee: currentEmployee._id },
                { toEmployee: currentEmployee._id }
              ]
            }
          ]
        };
      }
    } else {
      // Anonymous users can only see public thanks
      privacyFilter = { isPublic: true };
    }

    let query: any = {
      ...privacyFilter,
      // CRITICAL: Always filter by organization for multi-tenant isolation
      organization: orgContext.organization._id
    };

    if (category) {
      query.category = category;
    }

    if (toEmployee) {
      query.toEmployee = toEmployee;
    }

    if (fromEmployee) {
      query.fromEmployee = fromEmployee;
    }

    const thanks = await Thanks.find(query)
      .populate('fromEmployee', 'name email image department position')
      .populate('toEmployee', 'name email image department position')
      .select('-__v')
      .limit(limit)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Thanks.countDocuments(query);

    return NextResponse.json({
      thanks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching thanks:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    await dbConnect();

    const body = await request.json();
    const { toEmployee, message, category, isPublic = true, aiGenerated = false, anonymousSender } = body;

    // Validate required fields
    if (!toEmployee || !message || !category) {
      return NextResponse.json(
        { error: 'To employee, message, and category are required' },
        { status: 400 }
      );
    }

    // For anonymous users, validate anonymous sender info
    if (!session?.user?.email && (!anonymousSender?.name || !anonymousSender.name.trim())) {
      return NextResponse.json(
        { error: 'Anonymous sender name is required' },
        { status: 400 }
      );
    }

    // First, find the target employee to determine their organization
    const toEmployeeRecord = await Employee.findById(toEmployee);
    if (!toEmployeeRecord) {
      return NextResponse.json(
        { error: 'Target employee not found' },
        { status: 404 }
      );
    }

    // Get organization context for both authenticated and anonymous users
    const orgContext = await getOrganizationFromRequest(request);

    let fromEmployeeRecord = null;

    if (session?.user?.email) {
      // For authenticated users, try to find them in the target employee's organization first
      fromEmployeeRecord = await Employee.findOne({
        email: session.user.email,
        organization: toEmployeeRecord.organization
      });

      // If not found in target's organization, try to find in any organization
      if (!fromEmployeeRecord) {
        fromEmployeeRecord = await Employee.findOne({
          email: session.user.email
        });
      }

      if (!fromEmployeeRecord) {
        return NextResponse.json(
          { error: 'Employee record not found' },
          { status: 404 }
        );
      }
    }

    // Prevent self-thanks for authenticated users
    if (fromEmployeeRecord && fromEmployeeRecord._id.toString() === toEmployee) {
      return NextResponse.json(
        { error: 'Cannot send thanks to yourself' },
        { status: 400 }
      );
    }

    const thanksData: any = {
      organization: toEmployeeRecord.organization, // Use target employee's organization
      toEmployee,
      message: message.trim(),
      category,
      isPublic,
      aiGenerated,
      reactions: []
    };

    // Add fromEmployee for authenticated users or anonymous sender info
    if (fromEmployeeRecord) {
      thanksData.fromEmployee = fromEmployeeRecord._id;
    } else {
      thanksData.anonymousSender = {
        name: anonymousSender.name.trim()
      };
    }

    const thanks = new Thanks(thanksData);
    await thanks.save();

    // Populate the response
    if (fromEmployeeRecord) {
      await thanks.populate('fromEmployee', 'name email image department position');
    }
    await thanks.populate('toEmployee', 'name email image department position');

    // Create notification for the recipient (only for authenticated users)
    if (fromEmployeeRecord) {
      try {
        await NotificationService.createThanksNotification(
          thanks._id,
          toEmployee,
          fromEmployeeRecord._id,
          fromEmployeeRecord.name,
          category,
          toEmployeeRecord.organization // Use target employee's organization
        );
      } catch (notificationError) {
        console.error('Error creating notification:', notificationError);
        // Don't fail the thanks creation if notification fails
      }
    }

    return NextResponse.json(thanks, { status: 201 });

  } catch (error) {
    console.error('Error creating thanks:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
